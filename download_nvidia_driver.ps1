# NVIDIA Tesla V100 Driver Download Script
# Download latest Data Center Driver

Write-Host "Downloading latest driver for Tesla V100..." -ForegroundColor Green

# Create download directory
$downloadPath = "$env:USERPROFILE\Downloads\NVIDIA_Driver"
if (!(Test-Path $downloadPath)) {
    New-Item -ItemType Directory -Path $downloadPath -Force
    Write-Host "Created download directory: $downloadPath" -ForegroundColor Yellow
}

# Driver download information
$driverVersion = "551.78"
$driverUrl = "https://us.download.nvidia.com/tesla/551.78/551.78-data-center-tesla-desktop-winserver-2019-2022-dch-international.exe"
$fileName = "NVIDIA_DataCenter_Driver_$driverVersion.exe"
$fullPath = Join-Path $downloadPath $fileName

Write-Host "Download URL: $driverUrl" -ForegroundColor Cyan
Write-Host "Save path: $fullPath" -ForegroundColor Cyan

try {
    # Download using Invoke-WebRequest
    Write-Host "Starting driver download, please wait..." -ForegroundColor Yellow
    Invoke-WebRequest -Uri $driverUrl -OutFile $fullPath -UseBasicParsing

    if (Test-Path $fullPath) {
        $fileSize = (Get-Item $fullPath).Length / 1MB
        Write-Host "Download completed!" -ForegroundColor Green
        Write-Host "File size: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Green
        Write-Host "File location: $fullPath" -ForegroundColor Green

        # Ask if install immediately
        $install = Read-Host "Install driver now? (Y/N)"
        if ($install -eq "Y" -or $install -eq "y") {
            Write-Host "Starting driver installation..." -ForegroundColor Yellow
            Start-Process -FilePath $fullPath -Wait
        } else {
            Write-Host "Driver downloaded, you can install it manually later." -ForegroundColor Blue
        }
    } else {
        Write-Host "Download failed! File not found." -ForegroundColor Red
    }
} catch {
    Write-Host "Error during download: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please try manual download:" -ForegroundColor Yellow
    Write-Host "1. Visit: https://www.nvidia.com/en-us/drivers/details/222668/" -ForegroundColor Cyan
    Write-Host "2. Click 'Download' button" -ForegroundColor Cyan
    Write-Host "3. Run the downloaded installer" -ForegroundColor Cyan
}

Write-Host "Please restart computer after installation to activate driver." -ForegroundColor Magenta
