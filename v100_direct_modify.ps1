# Direct V100 Registry Modification Script
# WARNING: This modifies system registry!

Write-Host "=== Tesla V100 Direct Registry Modification ===" -ForegroundColor Red
Write-Host "Proceeding with registry modifications..." -ForegroundColor Yellow

# Create backup
$backupPath = "$env:USERPROFILE\Desktop\v100_registry_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').reg"
Write-Host "Creating registry backup..." -ForegroundColor Yellow

try {
    reg export "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}" $backupPath
    Write-Host "✓ Registry backup created: $backupPath" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to create backup: $($_.Exception.Message)" -ForegroundColor Red
}

# Find Tesla V100 registry key
Write-Host "Searching for Tesla V100..." -ForegroundColor Yellow
$graphicsPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}"
$v100Key = $null

Get-ChildItem $graphicsPath | ForEach-Object {
    $keyPath = $_.PSPath
    try {
        $driverDesc = Get-ItemProperty -Path $keyPath -Name "DriverDesc" -ErrorAction SilentlyContinue
        if ($driverDesc.DriverDesc -like "*Tesla V100*") {
            $v100Key = $keyPath
            Write-Host "✓ Found Tesla V100 at: $($_.Name)" -ForegroundColor Green
        }
    } catch {
        # Ignore
    }
}

if (-not $v100Key) {
    Write-Host "✗ Tesla V100 not found!" -ForegroundColor Red
    exit 1
}

# Show current values
Write-Host "`nCurrent V100 Registry Values:" -ForegroundColor Cyan
try {
    $currentValues = Get-ItemProperty -Path $v100Key
    Write-Host "FeatureScore: $($currentValues.FeatureScore) (hex: $([Convert]::ToString($currentValues.FeatureScore, 16)))" -ForegroundColor White
    Write-Host "AdapterType: $($currentValues.AdapterType)" -ForegroundColor White
    Write-Host "EnableMsHybrid: $($currentValues.EnableMsHybrid)" -ForegroundColor White
    Write-Host "GridLicensedFeatures: $($currentValues.GridLicensedFeatures)" -ForegroundColor White
} catch {
    Write-Host "Could not read current values" -ForegroundColor Yellow
}

Write-Host "`nApplying modifications..." -ForegroundColor Yellow

try {
    # Step 1: Set AdapterType to 1
    Set-ItemProperty -Path $v100Key -Name "AdapterType" -Value 1 -Type DWord
    Write-Host "✓ Set AdapterType to 1" -ForegroundColor Green
    
    # Step 2: Set FeatureScore to 0xd1 (209 decimal)
    Set-ItemProperty -Path $v100Key -Name "FeatureScore" -Value 0xd1 -Type DWord
    Write-Host "✓ Set FeatureScore to d1 (hex)" -ForegroundColor Green
    
    # Step 3: Set GridLicensedFeatures to 7
    Set-ItemProperty -Path $v100Key -Name "GridLicensedFeatures" -Value 7 -Type DWord
    Write-Host "✓ Set GridLicensedFeatures to 7" -ForegroundColor Green
    
    # Step 4: Remove AdapterType (as per blog)
    Remove-ItemProperty -Path $v100Key -Name "AdapterType" -ErrorAction SilentlyContinue
    Write-Host "✓ Removed AdapterType" -ForegroundColor Green
    
    # Step 5: Set EnableMsHybrid to 1
    Set-ItemProperty -Path $v100Key -Name "EnableMsHybrid" -Value 1 -Type DWord
    Write-Host "✓ Set EnableMsHybrid to 1" -ForegroundColor Green
    
} catch {
    Write-Host "✗ Error modifying registry: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Find and modify Intel graphics
Write-Host "`nSearching for Intel graphics..." -ForegroundColor Yellow
$intelKey = $null

Get-ChildItem $graphicsPath | ForEach-Object {
    $keyPath = $_.PSPath
    try {
        $driverDesc = Get-ItemProperty -Path $keyPath -Name "DriverDesc" -ErrorAction SilentlyContinue
        if ($driverDesc.DriverDesc -like "*Intel*") {
            $intelKey = $keyPath
            Write-Host "✓ Found Intel graphics at: $($_.Name)" -ForegroundColor Green
        }
    } catch {
        # Ignore
    }
}

if ($intelKey) {
    try {
        Set-ItemProperty -Path $intelKey -Name "EnableMsHybrid" -Value 2 -Type DWord
        Write-Host "✓ Set Intel EnableMsHybrid to 2" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Could not modify Intel settings: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ Intel graphics not found" -ForegroundColor Yellow
}

# Configure DirectX settings
Write-Host "`nConfiguring DirectX GPU preference..." -ForegroundColor Yellow
$v100HardwareId = "10DE&1DB1&121210DE"  # Standard V100 ID

try {
    $directxPath = "HKCU:\SOFTWARE\Microsoft\DirectX"
    if (-not (Test-Path $directxPath)) {
        New-Item -Path $directxPath -Force | Out-Null
    }
    
    $userGpuPath = "$directxPath\UserGpuPreferences"
    if (-not (Test-Path $userGpuPath)) {
        New-Item -Path $userGpuPath -Force | Out-Null
    }
    
    Set-ItemProperty -Path $directxPath -Name "DirectXUserGlobalSettings" -Value "HighPerfAdapter=$v100HardwareId" -Type String
    Write-Host "✓ Set DirectX high performance adapter to V100" -ForegroundColor Green
    
} catch {
    Write-Host "⚠ Could not set DirectX preference: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Show final values
Write-Host "`nFinal V100 Registry Values:" -ForegroundColor Cyan
try {
    $finalValues = Get-ItemProperty -Path $v100Key
    Write-Host "FeatureScore: $($finalValues.FeatureScore) (hex: $([Convert]::ToString($finalValues.FeatureScore, 16)))" -ForegroundColor White
    Write-Host "AdapterType: $($finalValues.AdapterType)" -ForegroundColor White
    Write-Host "EnableMsHybrid: $($finalValues.EnableMsHybrid)" -ForegroundColor White
    Write-Host "GridLicensedFeatures: $($finalValues.GridLicensedFeatures)" -ForegroundColor White
} catch {
    Write-Host "Could not read final values" -ForegroundColor Yellow
}

Write-Host "`n=== Modification Complete ===" -ForegroundColor Green
Write-Host "Registry backup: $backupPath" -ForegroundColor Cyan
Write-Host "`nNEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Open Device Manager" -ForegroundColor White
Write-Host "2. Disable Tesla V100 under Display adapters" -ForegroundColor White
Write-Host "3. Re-enable Tesla V100" -ForegroundColor White
Write-Host "4. Restart computer" -ForegroundColor White
Write-Host "5. Test graphics performance" -ForegroundColor White
Write-Host "`nIf problems occur, restore from backup: $backupPath" -ForegroundColor Red

Write-Host "`nPress Enter to exit..." -ForegroundColor Gray
Read-Host
