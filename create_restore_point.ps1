# Create System Restore Point Script

Write-Host "Creating System Restore Point..." -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

try {
    # Enable System Restore if not enabled
    Enable-ComputerRestore -Drive "C:\"
    
    # Create restore point
    $restorePointName = "Before V100 Graphics Modification - $(Get-Date -Format 'yyyy-MM-dd HH:mm')"
    Checkpoint-Computer -Description $restorePointName -RestorePointType "MODIFY_SETTINGS"
    
    Write-Host "System Restore Point created successfully!" -ForegroundColor Green
    Write-Host "Restore Point Name: $restorePointName" -ForegroundColor Cyan
    
} catch {
    Write-Host "Error creating restore point: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "You may need to enable System Restore manually." -ForegroundColor Yellow
}

Read-Host "Press Enter to continue"
