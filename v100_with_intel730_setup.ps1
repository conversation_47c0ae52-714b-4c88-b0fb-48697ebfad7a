# Tesla V100 + Intel UHD Graphics 730 Setup Script
# Based on CSDN tutorial, customized for your specific hardware

Write-Host "=== Tesla V100 + Intel UHD Graphics 730 Setup ===" -ForegroundColor Green
Write-Host "Configuring V100 to work with Intel UHD Graphics 730 for display output" -ForegroundColor Cyan

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Get current hardware information
Write-Host "`nDetecting hardware..." -ForegroundColor Yellow

# Get Intel UHD Graphics 730 info
$intelGpu = Get-CimInstance -ClassName Win32_VideoController | Where-Object {$_.Name -like "*Intel*"}
$v100Gpu = Get-CimInstance -ClassName Win32_VideoController | Where-Object {$_.Name -like "*Tesla V100*"}

if ($intelGpu) {
    Write-Host "✓ Found Intel GPU: $($intelGpu.Name)" -ForegroundColor Green
    Write-Host "  PNP Device ID: $($intelGpu.PNPDeviceID)" -ForegroundColor Gray
    
    # Extract Intel hardware ID
    if ($intelGpu.PNPDeviceID -match "VEN_([0-9A-F]{4}).*DEV_([0-9A-F]{4}).*SUBSYS_([0-9A-F]{8})") {
        $intelHardwareId = "$($matches[1])&$($matches[2])&$($matches[3])"
        Write-Host "  Intel Hardware ID: $intelHardwareId" -ForegroundColor Cyan
    } else {
        $intelHardwareId = "8086&4682&22128086"  # Default for UHD 730
        Write-Host "  Using default Intel UHD 730 ID: $intelHardwareId" -ForegroundColor Yellow
    }
} else {
    Write-Host "✗ Intel GPU not found!" -ForegroundColor Red
    exit 1
}

if ($v100Gpu) {
    Write-Host "✓ Found V100: $($v100Gpu.Name)" -ForegroundColor Green
    Write-Host "  PNP Device ID: $($v100Gpu.PNPDeviceID)" -ForegroundColor Gray
    
    # Extract V100 hardware ID
    if ($v100Gpu.PNPDeviceID -match "VEN_([0-9A-F]{4}).*DEV_([0-9A-F]{4}).*SUBSYS_([0-9A-F]{8})") {
        $v100HardwareId = "$($matches[1])&$($matches[2])&$($matches[3])"
        Write-Host "  V100 Hardware ID: $v100HardwareId" -ForegroundColor Cyan
    } else {
        $v100HardwareId = "10DE&1DB1&121210DE"  # Default V100 ID
        Write-Host "  Using default V100 ID: $v100HardwareId" -ForegroundColor Yellow
    }
} else {
    Write-Host "✗ Tesla V100 not found!" -ForegroundColor Red
    exit 1
}

$confirm = Read-Host "`nProceed with V100 + Intel UHD 730 configuration? (yes/no)"
if ($confirm -ne "yes") {
    Write-Host "Operation cancelled." -ForegroundColor Yellow
    exit 0
}

# Create backup
Write-Host "`nCreating registry backup..." -ForegroundColor Yellow
$backupPath = "$env:USERPROFILE\Desktop\v100_intel730_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').reg"

try {
    reg export "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}" $backupPath
    Write-Host "✓ Registry backup created: $backupPath" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to create backup: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Find registry keys
Write-Host "`nFinding GPU registry keys..." -ForegroundColor Yellow
$graphicsPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}"
$v100Key = $null
$intelKey = $null

Get-ChildItem $graphicsPath -ErrorAction SilentlyContinue | ForEach-Object {
    $keyPath = $_.PSPath
    try {
        $driverDesc = Get-ItemProperty -Path $keyPath -Name "DriverDesc" -ErrorAction SilentlyContinue
        if ($driverDesc.DriverDesc -like "*Tesla V100*") {
            $v100Key = $keyPath
            Write-Host "✓ Found V100 registry key: $($_.Name)" -ForegroundColor Green
        }
        if ($driverDesc.DriverDesc -like "*Intel*UHD*730*") {
            $intelKey = $keyPath
            Write-Host "✓ Found Intel UHD 730 registry key: $($_.Name)" -ForegroundColor Green
        }
    } catch { }
}

if (-not $v100Key) {
    Write-Host "✗ V100 registry key not found!" -ForegroundColor Red
    exit 1
}

if (-not $intelKey) {
    Write-Host "✗ Intel UHD 730 registry key not found!" -ForegroundColor Red
    exit 1
}

# Configure V100 for graphics mode
Write-Host "`nConfiguring Tesla V100..." -ForegroundColor Yellow

try {
    # Step 1: Set AdapterType to 1 (display adapter)
    Set-ItemProperty -Path $v100Key -Name "AdapterType" -Value 1 -Type DWord
    Write-Host "✓ Set V100 AdapterType to 1" -ForegroundColor Green
    
    # Step 2: Set FeatureScore to d1 (better than Intel)
    Set-ItemProperty -Path $v100Key -Name "FeatureScore" -Value 0xd1 -Type DWord
    Write-Host "✓ Set V100 FeatureScore to d1" -ForegroundColor Green
    
    # Step 3: Enable GRID features
    Set-ItemProperty -Path $v100Key -Name "GridLicensedFeatures" -Value 7 -Type DWord
    Write-Host "✓ Set V100 GridLicensedFeatures to 7" -ForegroundColor Green
    
    # Step 4: Remove AdapterType (as per tutorial)
    Start-Sleep -Seconds 1
    Remove-ItemProperty -Path $v100Key -Name "AdapterType" -ErrorAction SilentlyContinue
    Write-Host "✓ Removed V100 AdapterType" -ForegroundColor Green
    
    # Step 5: Enable hybrid mode for V100
    Set-ItemProperty -Path $v100Key -Name "EnableMsHybrid" -Value 1 -Type DWord
    Write-Host "✓ Set V100 EnableMsHybrid to 1" -ForegroundColor Green
    
} catch {
    Write-Host "✗ Error configuring V100: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Configure Intel UHD 730 as output adapter
Write-Host "`nConfiguring Intel UHD Graphics 730..." -ForegroundColor Yellow

try {
    # Set Intel as hybrid output adapter (value 2)
    Set-ItemProperty -Path $intelKey -Name "EnableMsHybrid" -Value 2 -Type DWord
    Write-Host "✓ Set Intel UHD 730 EnableMsHybrid to 2 (output adapter)" -ForegroundColor Green
    
} catch {
    Write-Host "✗ Error configuring Intel UHD 730: $($_.Exception.Message)" -ForegroundColor Red
}

# Configure DirectX to prefer V100 for high performance
Write-Host "`nConfiguring DirectX GPU preference..." -ForegroundColor Yellow

try {
    $directxPath = "HKCU:\SOFTWARE\Microsoft\DirectX"
    if (-not (Test-Path $directxPath)) {
        New-Item -Path $directxPath -Force | Out-Null
    }
    
    $userGpuPath = "$directxPath\UserGpuPreferences"
    if (-not (Test-Path $userGpuPath)) {
        New-Item -Path $userGpuPath -Force | Out-Null
    }
    
    # Set V100 as high performance adapter
    Set-ItemProperty -Path $directxPath -Name "DirectXUserGlobalSettings" -Value "HighPerfAdapter=$v100HardwareId" -Type String
    Write-Host "✓ Set DirectX high performance adapter to V100" -ForegroundColor Green
    
} catch {
    Write-Host "⚠ Could not set DirectX preference: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Show final configuration
Write-Host "`n=== Final Configuration ===" -ForegroundColor Cyan

Write-Host "V100 Settings:" -ForegroundColor White
try {
    $v100Values = Get-ItemProperty -Path $v100Key
    Write-Host "  FeatureScore: $($v100Values.FeatureScore) (hex: $([Convert]::ToString($v100Values.FeatureScore, 16)))" -ForegroundColor Gray
    Write-Host "  EnableMsHybrid: $($v100Values.EnableMsHybrid)" -ForegroundColor Gray
    Write-Host "  GridLicensedFeatures: $($v100Values.GridLicensedFeatures)" -ForegroundColor Gray
} catch {
    Write-Host "  Could not read V100 values" -ForegroundColor Yellow
}

Write-Host "Intel UHD 730 Settings:" -ForegroundColor White
try {
    $intelValues = Get-ItemProperty -Path $intelKey
    Write-Host "  EnableMsHybrid: $($intelValues.EnableMsHybrid)" -ForegroundColor Gray
} catch {
    Write-Host "  Could not read Intel values" -ForegroundColor Yellow
}

Write-Host "Hardware IDs:" -ForegroundColor White
Write-Host "  V100: $v100HardwareId" -ForegroundColor Gray
Write-Host "  Intel UHD 730: $intelHardwareId" -ForegroundColor Gray

Write-Host "`n=== Setup Complete ===" -ForegroundColor Green
Write-Host "Configuration based on your specific hardware:" -ForegroundColor Cyan
Write-Host "- Tesla V100: High performance GPU" -ForegroundColor White
Write-Host "- Intel UHD Graphics 730: Display output" -ForegroundColor White

Write-Host "`nNEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Open Device Manager" -ForegroundColor White
Write-Host "2. Disable Tesla V100 under Display adapters" -ForegroundColor White
Write-Host "3. Re-enable Tesla V100" -ForegroundColor White
Write-Host "4. Restart computer" -ForegroundColor White
Write-Host "5. Test SolidWorks performance" -ForegroundColor White

Write-Host "`nBackup saved to: $backupPath" -ForegroundColor Gray
Read-Host "`nPress Enter to exit"
