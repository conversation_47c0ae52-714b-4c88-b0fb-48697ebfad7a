# Tesla V100 Graphics Enable Script
# Based on CSDN blog method - PROCEED WITH CAUTION!

Write-Host "=== Tesla V100 Graphics Enable Script ===" -ForegroundColor Red
Write-Host "WARNING: This script modifies system registry!" -ForegroundColor Red
Write-Host "Make sure you have created a system restore point!" -ForegroundColor Yellow
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Confirm before proceeding
$confirm = Read-Host "Do you want to proceed? This will modify registry! (yes/no)"
if ($confirm -ne "yes") {
    Write-Host "Operation cancelled." -ForegroundColor Yellow
    exit 0
}

Write-Host "Creating registry backup..." -ForegroundColor Yellow
$backupPath = "$env:USERPROFILE\Desktop\registry_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').reg"

try {
    # Backup graphics drivers registry
    reg export "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}" $backupPath
    Write-Host "Registry backup created: $backupPath" -ForegroundColor Green
} catch {
    Write-Host "Failed to create backup: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "Searching for Tesla V100 in registry..." -ForegroundColor Yellow

# Find Tesla V100 registry key
$graphicsPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}"
$v100Key = $null

Get-ChildItem $graphicsPath | ForEach-Object {
    $keyPath = $_.PSPath
    try {
        $driverDesc = Get-ItemProperty -Path $keyPath -Name "DriverDesc" -ErrorAction SilentlyContinue
        if ($driverDesc.DriverDesc -like "*Tesla V100*") {
            $v100Key = $keyPath
            Write-Host "Found Tesla V100 at: $keyPath" -ForegroundColor Green
        }
    } catch {
        # Ignore keys without DriverDesc
    }
}

if (-not $v100Key) {
    Write-Host "Tesla V100 not found in registry!" -ForegroundColor Red
    exit 1
}

Write-Host "Modifying Tesla V100 registry settings..." -ForegroundColor Yellow

try {
    # Step 1: Modify AdapterType to 1
    Set-ItemProperty -Path $v100Key -Name "AdapterType" -Value 1 -Type DWord
    Write-Host "✓ Set AdapterType to 1" -ForegroundColor Green
    
    # Step 2: Modify FeatureScore from cf to d1 (hex)
    Set-ItemProperty -Path $v100Key -Name "FeatureScore" -Value 0xd1 -Type DWord
    Write-Host "✓ Set FeatureScore to d1" -ForegroundColor Green
    
    # Step 3: Create GridLicensedFeatures with value 7
    Set-ItemProperty -Path $v100Key -Name "GridLicensedFeatures" -Value 7 -Type DWord
    Write-Host "✓ Set GridLicensedFeatures to 7" -ForegroundColor Green
    
    # Step 4: Remove AdapterType (as per blog instructions)
    Remove-ItemProperty -Path $v100Key -Name "AdapterType" -ErrorAction SilentlyContinue
    Write-Host "✓ Removed AdapterType" -ForegroundColor Green
    
    # Step 5: Create EnableMsHybrid with value 1
    Set-ItemProperty -Path $v100Key -Name "EnableMsHybrid" -Value 1 -Type DWord
    Write-Host "✓ Set EnableMsHybrid to 1" -ForegroundColor Green
    
} catch {
    Write-Host "Error modifying V100 registry: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Find Intel graphics and set EnableMsHybrid to 2
Write-Host "Searching for Intel graphics..." -ForegroundColor Yellow
$intelKey = $null

Get-ChildItem $graphicsPath | ForEach-Object {
    $keyPath = $_.PSPath
    try {
        $driverDesc = Get-ItemProperty -Path $keyPath -Name "DriverDesc" -ErrorAction SilentlyContinue
        if ($driverDesc.DriverDesc -like "*Intel*") {
            $intelKey = $keyPath
            Write-Host "Found Intel graphics at: $keyPath" -ForegroundColor Green
        }
    } catch {
        # Ignore keys without DriverDesc
    }
}

if ($intelKey) {
    try {
        Set-ItemProperty -Path $intelKey -Name "EnableMsHybrid" -Value 2 -Type DWord
        Write-Host "✓ Set Intel EnableMsHybrid to 2" -ForegroundColor Green
    } catch {
        Write-Host "Warning: Could not set Intel EnableMsHybrid: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Get V100 hardware ID for DirectX settings
Write-Host "Getting V100 hardware ID..." -ForegroundColor Yellow
$v100HardwareId = $null

try {
    $hardwareId = Get-ItemProperty -Path $v100Key -Name "MatchingDeviceId" -ErrorAction SilentlyContinue
    if ($hardwareId) {
        # Extract VEN, DEV, and SUBSYS from hardware ID
        $id = $hardwareId.MatchingDeviceId
        if ($id -match "VEN_([0-9A-F]{4}).*DEV_([0-9A-F]{4}).*SUBSYS_([0-9A-F]{8})") {
            $v100HardwareId = "$($matches[1])&$($matches[2])&$($matches[3])"
            Write-Host "V100 Hardware ID: $v100HardwareId" -ForegroundColor Green
        }
    }
} catch {
    Write-Host "Could not extract hardware ID automatically" -ForegroundColor Yellow
    $v100HardwareId = "10DE&1DB1&121210DE"  # Default V100 ID from blog
    Write-Host "Using default V100 ID: $v100HardwareId" -ForegroundColor Yellow
}

# Configure DirectX GPU preference
Write-Host "Configuring DirectX GPU preference..." -ForegroundColor Yellow

try {
    $directxPath = "HKCU:\SOFTWARE\Microsoft\DirectX"
    if (-not (Test-Path $directxPath)) {
        New-Item -Path $directxPath -Force | Out-Null
    }
    
    $userGpuPath = "$directxPath\UserGpuPreferences"
    if (-not (Test-Path $userGpuPath)) {
        New-Item -Path $userGpuPath -Force | Out-Null
    }
    
    Set-ItemProperty -Path $directxPath -Name "DirectXUserGlobalSettings" -Value "HighPerfAdapter=$v100HardwareId" -Type String
    Write-Host "✓ Set DirectX high performance adapter to V100" -ForegroundColor Green
    
} catch {
    Write-Host "Error setting DirectX preference: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Modification Complete ===" -ForegroundColor Green
Write-Host "Registry backup saved to: $backupPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "IMPORTANT NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Disable Tesla V100 in Device Manager" -ForegroundColor White
Write-Host "2. Re-enable Tesla V100 in Device Manager" -ForegroundColor White
Write-Host "3. Restart your computer" -ForegroundColor White
Write-Host "4. Test graphics performance" -ForegroundColor White
Write-Host ""
Write-Host "If something goes wrong, restore registry from: $backupPath" -ForegroundColor Red

Read-Host "Press Enter to exit"
