# Restore V100 to Original TCC Mode Script

Write-Host "=== Tesla V100 Restore to Original Mode ===" -ForegroundColor Yellow
Write-Host "This will restore V100 to its original TCC (compute) mode" -ForegroundColor Cyan

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

$confirm = Read-Host "Do you want to restore V100 to original TCC mode? (yes/no)"
if ($confirm -ne "yes") {
    Write-Host "Operation cancelled." -ForegroundColor Yellow
    exit 0
}

# Create backup before restoration
Write-Host "Creating backup of current registry..." -ForegroundColor Yellow
$backupPath = "$env:USERPROFILE\Desktop\v100_before_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss').reg"

try {
    reg export "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}" $backupPath
    Write-Host "✓ Backup created: $backupPath" -ForegroundColor Green
} catch {
    Write-Host "⚠ Could not create backup: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Find Tesla V100 registry key
Write-Host "Finding Tesla V100 registry key..." -ForegroundColor Yellow
$graphicsPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}"
$v100Key = $null

Get-ChildItem $graphicsPath -ErrorAction SilentlyContinue | ForEach-Object {
    $keyPath = $_.PSPath
    try {
        $driverDesc = Get-ItemProperty -Path $keyPath -Name "DriverDesc" -ErrorAction SilentlyContinue
        if ($driverDesc.DriverDesc -like "*Tesla V100*") {
            $v100Key = $keyPath
            Write-Host "✓ Found Tesla V100 at: $($_.Name)" -ForegroundColor Green
        }
    } catch { }
}

if (-not $v100Key) {
    Write-Host "✗ Tesla V100 not found in registry!" -ForegroundColor Red
    exit 1
}

# Show current values
Write-Host "`nCurrent V100 Registry Values:" -ForegroundColor Cyan
try {
    $currentValues = Get-ItemProperty -Path $v100Key
    Write-Host "FeatureScore: $($currentValues.FeatureScore) (hex: $([Convert]::ToString($currentValues.FeatureScore, 16)))" -ForegroundColor White
    Write-Host "AdapterType: $($currentValues.AdapterType)" -ForegroundColor White
    Write-Host "EnableMsHybrid: $($currentValues.EnableMsHybrid)" -ForegroundColor White
    Write-Host "GridLicensedFeatures: $($currentValues.GridLicensedFeatures)" -ForegroundColor White
} catch {
    Write-Host "Could not read current values" -ForegroundColor Yellow
}

Write-Host "`nRestoring to original TCC mode values..." -ForegroundColor Yellow

try {
    # Restore original FeatureScore (CF = 207 decimal)
    Set-ItemProperty -Path $v100Key -Name "FeatureScore" -Value 207 -Type DWord
    Write-Host "✓ Restored FeatureScore to CF (207)" -ForegroundColor Green
    
    # Set AdapterType to 2 (compute mode)
    Set-ItemProperty -Path $v100Key -Name "AdapterType" -Value 2 -Type DWord
    Write-Host "✓ Set AdapterType to 2 (compute mode)" -ForegroundColor Green
    
    # Remove EnableMsHybrid
    Remove-ItemProperty -Path $v100Key -Name "EnableMsHybrid" -ErrorAction SilentlyContinue
    Write-Host "✓ Removed EnableMsHybrid" -ForegroundColor Green
    
    # Remove GridLicensedFeatures
    Remove-ItemProperty -Path $v100Key -Name "GridLicensedFeatures" -ErrorAction SilentlyContinue
    Write-Host "✓ Removed GridLicensedFeatures" -ForegroundColor Green
    
} catch {
    Write-Host "✗ Error restoring registry: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Also restore Intel graphics if modified
Write-Host "`nChecking Intel graphics settings..." -ForegroundColor Yellow
$intelKey = $null

Get-ChildItem $graphicsPath -ErrorAction SilentlyContinue | ForEach-Object {
    $keyPath = $_.PSPath
    try {
        $driverDesc = Get-ItemProperty -Path $keyPath -Name "DriverDesc" -ErrorAction SilentlyContinue
        if ($driverDesc.DriverDesc -like "*Intel*") {
            $intelKey = $keyPath
            Write-Host "✓ Found Intel graphics at: $($_.Name)" -ForegroundColor Green
        }
    } catch { }
}

if ($intelKey) {
    try {
        # Remove EnableMsHybrid from Intel
        Remove-ItemProperty -Path $intelKey -Name "EnableMsHybrid" -ErrorAction SilentlyContinue
        Write-Host "✓ Removed Intel EnableMsHybrid" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Could not modify Intel settings: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Remove DirectX settings
Write-Host "`nRemoving DirectX GPU preference..." -ForegroundColor Yellow
try {
    $directxPath = "HKCU:\SOFTWARE\Microsoft\DirectX"
    if (Test-Path $directxPath) {
        Remove-ItemProperty -Path $directxPath -Name "DirectXUserGlobalSettings" -ErrorAction SilentlyContinue
        Write-Host "✓ Removed DirectX GPU preference" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Could not remove DirectX settings: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Show final values
Write-Host "`nFinal V100 Registry Values:" -ForegroundColor Cyan
try {
    $finalValues = Get-ItemProperty -Path $v100Key
    Write-Host "FeatureScore: $($finalValues.FeatureScore) (hex: $([Convert]::ToString($finalValues.FeatureScore, 16)))" -ForegroundColor White
    Write-Host "AdapterType: $($finalValues.AdapterType)" -ForegroundColor White
    Write-Host "EnableMsHybrid: $($finalValues.EnableMsHybrid)" -ForegroundColor White
    Write-Host "GridLicensedFeatures: $($finalValues.GridLicensedFeatures)" -ForegroundColor White
} catch {
    Write-Host "Could not read final values" -ForegroundColor Yellow
}

Write-Host "`n=== Restoration Complete ===" -ForegroundColor Green
Write-Host "V100 has been restored to original TCC (compute) mode" -ForegroundColor Cyan
Write-Host "`nNEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Open Device Manager" -ForegroundColor White
Write-Host "2. Disable and re-enable Tesla V100" -ForegroundColor White
Write-Host "3. Restart computer" -ForegroundColor White
Write-Host "4. V100 should work normally for compute tasks" -ForegroundColor White
Write-Host "5. nvidia-smi should work again" -ForegroundColor White

Write-Host "`nBackup saved to: $backupPath" -ForegroundColor Gray
Read-Host "`nPress Enter to exit"
