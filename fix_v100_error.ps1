# Fix V100 Error Code 43 Script

Write-Host "=== Tesla V100 Error Fix Script ===" -ForegroundColor Yellow
Write-Host "Attempting to fix Error Code 43..." -ForegroundColor Cyan

# Check current status
Write-Host "`nCurrent V100 Status:" -ForegroundColor Cyan
Get-CimInstance -ClassName Win32_VideoController | Where-Object {$_.Name -like "*NVIDIA*"} | Select-Object Name, Status, DriverVersion

Write-Host "`nAttempting fixes..." -ForegroundColor Yellow

# Method 1: Restart NVIDIA services
Write-Host "1. Restarting NVIDIA services..." -ForegroundColor White
try {
    Get-Service | Where-Object {$_.Name -like "*nvidia*"} | ForEach-Object {
        Write-Host "   Restarting $($_.Name)..." -ForegroundColor Gray
        Restart-Service $_.Name -Force -ErrorAction SilentlyContinue
    }
    Write-Host "   ✓ Services restarted" -ForegroundColor Green
} catch {
    Write-Host "   ⚠ Could not restart some services" -ForegroundColor Yellow
}

# Method 2: Reset device in Device Manager
Write-Host "2. Attempting to reset V100 device..." -ForegroundColor White
try {
    # Get V100 device
    $v100Device = Get-CimInstance -ClassName Win32_PnPEntity | Where-Object {$_.Name -like "*Tesla V100*"}
    if ($v100Device) {
        Write-Host "   Found V100 device: $($v100Device.DeviceID)" -ForegroundColor Gray
        
        # Try to disable and re-enable
        $v100Device | Invoke-CimMethod -MethodName "Disable"
        Start-Sleep -Seconds 3
        $v100Device | Invoke-CimMethod -MethodName "Enable"
        Write-Host "   ✓ Device reset attempted" -ForegroundColor Green
    } else {
        Write-Host "   ⚠ V100 device not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ⚠ Could not reset device: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Method 3: Check and fix registry values
Write-Host "3. Checking registry values..." -ForegroundColor White
$graphicsPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}"
$v100Key = $null

# Find V100 key
Get-ChildItem $graphicsPath -ErrorAction SilentlyContinue | ForEach-Object {
    $keyPath = $_.PSPath
    try {
        $driverDesc = Get-ItemProperty -Path $keyPath -Name "DriverDesc" -ErrorAction SilentlyContinue
        if ($driverDesc.DriverDesc -like "*Tesla V100*") {
            $v100Key = $keyPath
        }
    } catch { }
}

if ($v100Key) {
    try {
        # Check if GridLicensedFeatures might be causing issues
        $currentValues = Get-ItemProperty -Path $v100Key -ErrorAction SilentlyContinue
        Write-Host "   Current FeatureScore: $($currentValues.FeatureScore)" -ForegroundColor Gray
        Write-Host "   Current EnableMsHybrid: $($currentValues.EnableMsHybrid)" -ForegroundColor Gray
        Write-Host "   Current GridLicensedFeatures: $($currentValues.GridLicensedFeatures)" -ForegroundColor Gray
        
        # Try removing GridLicensedFeatures temporarily
        Write-Host "   Temporarily removing GridLicensedFeatures..." -ForegroundColor Gray
        Remove-ItemProperty -Path $v100Key -Name "GridLicensedFeatures" -ErrorAction SilentlyContinue
        Write-Host "   ✓ Registry adjustment made" -ForegroundColor Green
        
    } catch {
        Write-Host "   ⚠ Could not modify registry: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "   ⚠ V100 registry key not found" -ForegroundColor Yellow
}

# Method 4: Force driver reinstall
Write-Host "4. Checking driver status..." -ForegroundColor White
try {
    # Check if nvidia-smi works now
    $nvidiaSmiPath = "C:\Windows\System32\nvidia-smi.exe"
    if (Test-Path $nvidiaSmiPath) {
        Write-Host "   Testing nvidia-smi..." -ForegroundColor Gray
        $result = & $nvidiaSmiPath 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✓ nvidia-smi working" -ForegroundColor Green
        } else {
            Write-Host "   ⚠ nvidia-smi still failing" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "   ⚠ Could not test nvidia-smi" -ForegroundColor Yellow
}

Write-Host "`nChecking final status..." -ForegroundColor Cyan
Start-Sleep -Seconds 5

# Check final status
$finalStatus = Get-CimInstance -ClassName Win32_VideoController | Where-Object {$_.Name -like "*NVIDIA*"}
if ($finalStatus) {
    Write-Host "Final V100 Status: $($finalStatus.Status)" -ForegroundColor $(if ($finalStatus.Status -eq "OK") { "Green" } else { "Red" })
    Write-Host "Driver Version: $($finalStatus.DriverVersion)" -ForegroundColor White
} else {
    Write-Host "V100 not detected" -ForegroundColor Red
}

Write-Host "`n=== Recommendations ===" -ForegroundColor Magenta
if ($finalStatus.Status -eq "OK") {
    Write-Host "✓ V100 appears to be working!" -ForegroundColor Green
    Write-Host "  - Test SolidWorks performance now" -ForegroundColor White
    Write-Host "  - Check Windows Graphics Settings" -ForegroundColor White
} else {
    Write-Host "⚠ V100 still has issues. Try these options:" -ForegroundColor Yellow
    Write-Host "  Option 1: Restore registry backup from desktop" -ForegroundColor White
    Write-Host "  Option 2: Reinstall NVIDIA driver" -ForegroundColor White
    Write-Host "  Option 3: Use System Restore" -ForegroundColor White
    Write-Host "  Option 4: Revert to TCC mode (safer)" -ForegroundColor White
}

Read-Host "`nPress Enter to exit"
