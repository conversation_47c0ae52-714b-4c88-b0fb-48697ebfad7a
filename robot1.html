<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- 引入 Three.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.153.0/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.153.0/examples/js/controls/OrbitControls.min.js"></script>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>4轴机械臂仿真控制器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .canvas-container {
            flex: 1;
            position: relative;
            background: radial-gradient(circle at center, #2c3e50 0%, #1a252f 100%);
            border-right: 2px solid #34495e;
        }

        #robotCanvas {
            width: 100%;
            height: 100%;
            display: block;
            position: absolute;
            top: 0;
            left: 0;
        }

        .control-panel {
            width: 350px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            backdrop-filter: blur(10px);
            border-left: 1px solid rgba(255, 255, 255, 0.1);
        }

        .panel-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #3498db;
        }

        .panel-header h1 {
            font-size: 24px;
            color: #3498db;
            margin-bottom: 5px;
        }

        .panel-header p {
            font-size: 14px;
            color: #bdc3c7;
        }

        .joint-control {
            margin-bottom: 25px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .joint-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .joint-name {
            font-weight: bold;
            color: #ecf0f1;
        }

        .joint-value {
            background: #3498db;
            color: white;
            padding: 2px 8px;
            border-radius: 15px;
            font-size: 12px;
            min-width: 45px;
            text-align: center;
        }

        .slider-container {
            position: relative;
        }

        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #34495e;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2980b9);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            transition: all 0.2s ease;
        }

        .slider::-webkit-slider-thumb:hover {
            transform: scale(1.2);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
        }

        .slider::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2980b9);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .slider-limits {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
            color: #95a5a6;
            margin-top: 5px;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-reset {
            background: #e74c3c;
            color: white;
        }

        .btn-reset:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }

        .btn-home {
            background: #27ae60;
            color: white;
        }

        .btn-home:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .status-panel {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-title {
            color: #f39c12;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .status-info {
            font-size: 12px;
            color: #bdc3c7;
            line-height: 1.4;
        }

        .grid-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image:
                linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="canvas-container">
            <div class="grid-background"></div>
            <canvas id="robotCanvas"></canvas>
        </div>

        <div class="control-panel">
            <div class="panel-header">
                <h1>机械臂控制台</h1>
                <p>4轴机械臂仿真系统</p>
            </div>

            <div class="joint-control">
                <div class="joint-label">
                    <span class="joint-name">基座旋转</span>
                    <span class="joint-value" id="joint1-value">0°</span>
                </div>
                <div class="slider-container">
                    <input type="range" class="slider" id="joint1" min="-180" max="180" value="0">
                    <div class="slider-limits">
                        <span>-180°</span>
                        <span>180°</span>
                    </div>
                </div>
            </div>

            <div class="joint-control">
                <div class="joint-label">
                    <span class="joint-name">肩部关节</span>
                    <span class="joint-value" id="joint2-value">0°</span>
                </div>
                <div class="slider-container">
                    <input type="range" class="slider" id="joint2" min="-90" max="90" value="0">
                    <div class="slider-limits">
                        <span>-90°</span>
                        <span>90°</span>
                    </div>
                </div>
            </div>

            <div class="joint-control">
                <div class="joint-label">
                    <span class="joint-name">肘部关节</span>
                    <span class="joint-value" id="joint3-value">0°</span>
                </div>
                <div class="slider-container">
                    <input type="range" class="slider" id="joint3" min="-135" max="135" value="0">
                    <div class="slider-limits">
                        <span>-135°</span>
                        <span>135°</span>
                    </div>
                </div>
            </div>

            <div class="joint-control">
                <div class="joint-label">
                    <span class="joint-name">腕部关节</span>
                    <span class="joint-value" id="joint4-value">0°</span>
                </div>
                <div class="slider-container">
                    <input type="range" class="slider" id="joint4" min="-180" max="180" value="0">
                    <div class="slider-limits">
                        <span>-180°</span>
                        <span>180°</span>
                    </div>
                </div>
            </div>
            <div class="joint-control">
                <div class="joint-label">
                    <span class="joint-name">视角选择</span>
                </div>
                <div class="slider-container">
                    <select id="viewSelector" style="width:100%;padding:6px;border-radius:5px;">
                        <option value="iso">等轴视角</option>
                        <option value="top">俯视</option>
                        <option value="side">侧视</option>
                        <option value="front">正视</option>
                        <option value="free">自由(鼠标)</option>
                    </select>
                </div>
            </div>

            <div class="control-buttons">
                <button class="btn btn-reset" onclick="resetRobot()">复位</button>
                <button class="btn btn-home" onclick="homePosition()">归零</button>
            </div>

            <div class="status-panel">
                <div class="status-title">系统状态</div>
                <div class="status-info">
                    <div>末端位置: <span id="end-position">计算中...</span></div>
                    <div>工作空间: 正常</div>
                    <div>关节状态: 运行中</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 3D 机械臂仿真部分 ---
        class RobotArm3D {
            constructor(canvasId) {
                this.canvas = document.getElementById(canvasId);
                // 让canvas自适应父容器尺寸
                this.resizeCanvas();
                this.scene = new THREE.Scene();
                this.camera = new THREE.PerspectiveCamera(45, this.width / this.height, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({ canvas: this.canvas, antialias: true, alpha: true });
                this.renderer.setClearColor(0x000000, 0);
                this.renderer.setSize(this.width, this.height, false);
                this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.15;
                this.controls.enablePan = false;
                this.controls.enableZoom = true;
                this.controls.minDistance = 180;
                this.controls.maxDistance = 600;
                this.controls.enableRotate = true;
                this.controls.target.set(0, 80, 0);

                // 机械臂参数
                this.baseHeight = 30;
                this.link1Length = 120;
                this.link2Length = 100;
                this.link3Length = 80;
                this.link4Length = 60;
                this.joints = [0, 0, 0, 0]; // 基座, 肩, 肘, 腕

                // 机械臂结构
                this.armGroup = new THREE.Group();
                this.scene.add(this.armGroup);
                this.createArm();
                this.createGround();
                this.createLights();
                this.setCameraView('iso');
                this.setupEventListeners();
                this.animate();
            }
            resizeCanvas() {
                // 让canvas填满父容器并获取其像素尺寸
                const parent = this.canvas.parentElement;
                this.width = parent.clientWidth;
                this.height = parent.clientHeight;
                this.canvas.width = this.width;
                this.canvas.height = this.height;
            }
            createArm() {
                // 清空旧结构
                while (this.armGroup.children.length) this.armGroup.remove(this.armGroup.children[0]);

                // 基座
                const baseGeo = new THREE.CylinderGeometry(28, 32, this.baseHeight, 32);
                const baseMat = new THREE.MeshPhongMaterial({ color: 0x34495e, shininess: 80 });
                this.baseMesh = new THREE.Mesh(baseGeo, baseMat);
                this.baseMesh.position.y = this.baseHeight / 2;
                this.armGroup.add(this.baseMesh);

                // 基座旋转组
                this.baseRotGroup = new THREE.Group();
                this.baseRotGroup.position.y = this.baseHeight;
                this.armGroup.add(this.baseRotGroup);

                // 肩部
                this.shoulderGroup = new THREE.Group();
                this.baseRotGroup.add(this.shoulderGroup);
                const link1Geo = new THREE.CylinderGeometry(10, 10, this.link1Length, 24);
                const link1Mat = new THREE.MeshPhongMaterial({ color: 0x3498db });
                this.link1 = new THREE.Mesh(link1Geo, link1Mat);
                this.link1.position.y = this.link1Length / 2;
                this.shoulderGroup.add(this.link1);
                // 肩关节球
                const joint1 = new THREE.Mesh(
                    new THREE.SphereGeometry(13, 24, 24),
                    new THREE.MeshPhongMaterial({ color: 0x2980b9 })
                );
                joint1.position.y = 0;
                this.shoulderGroup.add(joint1);

                // 肘部
                this.elbowGroup = new THREE.Group();
                this.elbowGroup.position.y = this.link1Length;
                this.shoulderGroup.add(this.elbowGroup);
                const link2Geo = new THREE.CylinderGeometry(9, 9, this.link2Length, 24);
                const link2Mat = new THREE.MeshPhongMaterial({ color: 0xe74c3c });
                this.link2 = new THREE.Mesh(link2Geo, link2Mat);
                this.link2.position.y = this.link2Length / 2;
                this.elbowGroup.add(this.link2);
                // 肘关节球
                const joint2 = new THREE.Mesh(
                    new THREE.SphereGeometry(11, 24, 24),
                    new THREE.MeshPhongMaterial({ color: 0xc0392b })
                );
                joint2.position.y = 0;
                this.elbowGroup.add(joint2);

                // 腕部
                this.wristGroup = new THREE.Group();
                this.wristGroup.position.y = this.link2Length;
                this.elbowGroup.add(this.wristGroup);
                const link3Geo = new THREE.CylinderGeometry(7, 7, this.link3Length, 24);
                const link3Mat = new THREE.MeshPhongMaterial({ color: 0x27ae60 });
                this.link3 = new THREE.Mesh(link3Geo, link3Mat);
                this.link3.position.y = this.link3Length / 2;
                this.wristGroup.add(this.link3);
                // 腕关节球
                const joint3 = new THREE.Mesh(
                    new THREE.SphereGeometry(9, 24, 24),
                    new THREE.MeshPhongMaterial({ color: 0x229954 })
                );
                joint3.position.y = 0;
                this.wristGroup.add(joint3);

                // 末端
                this.endGroup = new THREE.Group();
                this.endGroup.position.y = this.link3Length;
                this.wristGroup.add(this.endGroup);
                const link4Geo = new THREE.CylinderGeometry(5, 5, this.link4Length, 24);
                const link4Mat = new THREE.MeshPhongMaterial({ color: 0xf39c12 });
                this.link4 = new THREE.Mesh(link4Geo, link4Mat);
                this.link4.position.y = this.link4Length / 2;
                this.endGroup.add(this.link4);
                // 末端球
                const endSphere = new THREE.Mesh(
                    new THREE.SphereGeometry(12, 24, 24),
                    new THREE.MeshPhongMaterial({ color: 0xe67e22 })
                );
                endSphere.position.y = this.link4Length;
                this.endGroup.add(endSphere);

                // 末端抓手
                const gripperGeo = new THREE.BoxGeometry(14, 4, 4);
                const gripperMat = new THREE.MeshPhongMaterial({ color: 0xd35400 });
                const gripper1 = new THREE.Mesh(gripperGeo, gripperMat);
                gripper1.position.set(6, this.link4Length + 5, 5);
                const gripper2 = new THREE.Mesh(gripperGeo, gripperMat);
                gripper2.position.set(-6, this.link4Length + 5, -5);
                this.endGroup.add(gripper1);
                this.endGroup.add(gripper2);
            }
            createGround() {
                const grid = new THREE.GridHelper(400, 20, 0xaaaaaa, 0x444444);
                grid.position.y = 0;
                this.scene.add(grid);
            }
            createLights() {
                const ambient = new THREE.AmbientLight(0xffffff, 0.65);
                this.scene.add(ambient);
                const dir = new THREE.DirectionalLight(0xffffff, 0.7);
                dir.position.set(200, 400, 200);
                this.scene.add(dir);
            }
            setCameraView(view) {
                // 视角切换
                if (view === 'iso') {
                    this.camera.position.set(220, 220, 220);
                } else if (view === 'top') {
                    this.camera.position.set(0, 350, 0.01);
                } else if (view === 'side') {
                    this.camera.position.set(300, 80, 0);
                } else if (view === 'front') {
                    this.camera.position.set(0, 80, 300);
                }
                if (view !== 'free') {
                    this.controls.target.set(0, 80, 0);
                    this.controls.update();
                }
                this.controls.enabled = (view === 'free');
            }
            setupEventListeners() {
                // 关节滑块
                for (let i = 1; i <= 4; i++) {
                    const slider = document.getElementById(`joint${i}`);
                    const valueDisplay = document.getElementById(`joint${i}-value`);
                    slider.addEventListener('input', (e) => {
                        const angle = parseFloat(e.target.value);
                        this.joints[i-1] = angle * Math.PI / 180;
                        valueDisplay.textContent = `${angle}°`;
                        this.updateArm();
                        this.updateEndEffectorPosition();
                    });
                }
                // 视角选择
                document.getElementById('viewSelector').addEventListener('change', (e) => {
                    this.setCameraView(e.target.value);
                });
                // 自适应大小
                window.addEventListener('resize', () => {
                    this.resizeCanvas();
                    this.camera.aspect = this.width / this.height;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(this.width, this.height, false);
                });
            }
            updateArm() {
                // 关节正向运动学 (DH-like)
                // 基座旋转
                this.baseRotGroup.rotation.y = this.joints[0];
                // 肩部 (绕X轴)
                this.shoulderGroup.rotation.z = this.joints[1];
                // 肘部 (绕X轴)
                this.elbowGroup.rotation.z = this.joints[2];
                // 腕部 (绕X轴)
                this.wristGroup.rotation.z = this.joints[3];
            }
            getEndEffectorPosition() {
                // 计算末端执行器世界坐标
                this.updateArm();
                // 末端球在endGroup的本地坐标
                const pos = new THREE.Vector3(0, this.link4Length, 0);
                this.endGroup.localToWorld(pos);
                return pos;
            }
            updateEndEffectorPosition() {
                const pos = this.getEndEffectorPosition();
                const endPosElement = document.getElementById('end-position');
                endPosElement.textContent = `(${pos.x.toFixed(0)}, ${pos.y.toFixed(0)}, ${pos.z.toFixed(0)})`;
            }
            animate() {
                this.controls.update();
                this.renderer.render(this.scene, this.camera);
                requestAnimationFrame(() => this.animate());
            }
        }

        // 初始化机械臂
        let robotArm;
        window.addEventListener('DOMContentLoaded', () => {
            robotArm = new RobotArm3D('robotCanvas');
            // 初始化时刷新一次末端位置
            setTimeout(() => robotArm.updateEndEffectorPosition(), 100);
        });

        // 控制函数
        function resetRobot() {
            for (let i = 1; i <= 4; i++) {
                const slider = document.getElementById(`joint${i}`);
                const valueDisplay = document.getElementById(`joint${i}-value`);
                const randomAngle = Math.round(Math.random() * (slider.max - slider.min) + Number(slider.min));
                slider.value = randomAngle;
                robotArm.joints[i-1] = randomAngle * Math.PI / 180;
                valueDisplay.textContent = `${randomAngle}°`;
            }
            robotArm.updateArm();
            robotArm.updateEndEffectorPosition();
        }

        function homePosition() {
            for (let i = 1; i <= 4; i++) {
                const slider = document.getElementById(`joint${i}`);
                const valueDisplay = document.getElementById(`joint${i}-value`);
                slider.value = 0;
                robotArm.joints[i-1] = 0;
                valueDisplay.textContent = '0°';
            }
            robotArm.updateArm();
            robotArm.updateEndEffectorPosition();
        }

        // 添加键盘控制
        document.addEventListener('keydown', (e) => {
            const step = 5; // 5度步长
            let jointIndex = -1;
            let direction = 0;

            switch(e.key) {
                case '1': jointIndex = 0; direction = -1; break;
                case '2': jointIndex = 0; direction = 1; break;
                case '3': jointIndex = 1; direction = -1; break;
                case '4': jointIndex = 1; direction = 1; break;
                case '5': jointIndex = 2; direction = -1; break;
                case '6': jointIndex = 2; direction = 1; break;
                case '7': jointIndex = 3; direction = -1; break;
                case '8': jointIndex = 3; direction = 1; break;
                case 'r': resetRobot(); break;
                case 'h': homePosition(); break;
            }

            if (jointIndex >= 0) {
                const slider = document.getElementById(`joint${jointIndex + 1}`);
                const currentValue = parseFloat(slider.value);
                const newValue = Math.max(slider.min, Math.min(slider.max, currentValue + step * direction));

                slider.value = newValue;
                slider.dispatchEvent(new Event('input'));
            }
        });

    </script>
</body>
</html>
