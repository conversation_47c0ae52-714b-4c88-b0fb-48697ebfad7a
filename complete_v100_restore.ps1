# Complete Tesla V100 Restore Script
# This will completely restore V100 to its original working state

Write-Host "=== Complete Tesla V100 Restore ===" -ForegroundColor Red
Write-Host "This will restore V100 to its original TCC compute mode" -ForegroundColor Yellow
Write-Host "V100 will work normally for compute tasks, but not for graphics" -ForegroundColor Cyan

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "`nCurrent V100 Status:" -ForegroundColor Cyan
$v100Status = Get-CimInstance -ClassName Win32_VideoController | Where-Object {$_.Name -like "*Tesla V100*"}
if ($v100Status) {
    Write-Host "Status: $($v100Status.Status)" -ForegroundColor $(if ($v100Status.Status -eq "OK") { "Green" } else { "Red" })
    Write-Host "Error Code: $($v100Status.ConfigManagerErrorCode)" -ForegroundColor Red
} else {
    Write-Host "V100 not detected" -ForegroundColor Red
}

$confirm = Read-Host "`nProceed with complete restoration? (yes/no)"
if ($confirm -ne "yes") {
    Write-Host "Operation cancelled." -ForegroundColor Yellow
    exit 0
}

# Create final backup
Write-Host "`nCreating final backup..." -ForegroundColor Yellow
$backupPath = "$env:USERPROFILE\Desktop\v100_final_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').reg"

try {
    reg export "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}" $backupPath
    Write-Host "✓ Final backup created: $backupPath" -ForegroundColor Green
} catch {
    Write-Host "⚠ Could not create backup: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Find V100 and Intel registry keys
Write-Host "`nFinding GPU registry keys..." -ForegroundColor Yellow
$graphicsPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}"
$v100Key = $null
$intelKey = $null

Get-ChildItem $graphicsPath -ErrorAction SilentlyContinue | ForEach-Object {
    $keyPath = $_.PSPath
    try {
        $driverDesc = Get-ItemProperty -Path $keyPath -Name "DriverDesc" -ErrorAction SilentlyContinue
        if ($driverDesc.DriverDesc -like "*Tesla V100*") {
            $v100Key = $keyPath
            Write-Host "✓ Found V100 registry key" -ForegroundColor Green
        }
        if ($driverDesc.DriverDesc -like "*Intel*") {
            $intelKey = $keyPath
            Write-Host "✓ Found Intel registry key" -ForegroundColor Green
        }
    } catch { }
}

# Restore V100 to original TCC mode
if ($v100Key) {
    Write-Host "`nRestoring V100 to original TCC mode..." -ForegroundColor Yellow
    
    try {
        # Restore original FeatureScore (CF = 207 decimal)
        Set-ItemProperty -Path $v100Key -Name "FeatureScore" -Value 207 -Type DWord
        Write-Host "✓ Restored FeatureScore to CF (207)" -ForegroundColor Green
        
        # Set AdapterType to 2 (compute mode)
        Set-ItemProperty -Path $v100Key -Name "AdapterType" -Value 2 -Type DWord
        Write-Host "✓ Set AdapterType to 2 (compute mode)" -ForegroundColor Green
        
        # Remove all hybrid and GRID settings
        Remove-ItemProperty -Path $v100Key -Name "EnableMsHybrid" -ErrorAction SilentlyContinue
        Write-Host "✓ Removed EnableMsHybrid" -ForegroundColor Green
        
        Remove-ItemProperty -Path $v100Key -Name "GridLicensedFeatures" -ErrorAction SilentlyContinue
        Write-Host "✓ Removed GridLicensedFeatures" -ForegroundColor Green
        
    } catch {
        Write-Host "✗ Error restoring V100: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "⚠ V100 registry key not found" -ForegroundColor Yellow
}

# Restore Intel settings
if ($intelKey) {
    Write-Host "`nRestoring Intel settings..." -ForegroundColor Yellow
    
    try {
        # Remove hybrid settings from Intel
        Remove-ItemProperty -Path $intelKey -Name "EnableMsHybrid" -ErrorAction SilentlyContinue
        Write-Host "✓ Removed Intel EnableMsHybrid" -ForegroundColor Green
        
    } catch {
        Write-Host "⚠ Could not modify Intel settings: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ Intel registry key not found" -ForegroundColor Yellow
}

# Remove DirectX settings
Write-Host "`nRemoving DirectX GPU preferences..." -ForegroundColor Yellow
try {
    $directxPath = "HKCU:\SOFTWARE\Microsoft\DirectX"
    if (Test-Path $directxPath) {
        Remove-ItemProperty -Path $directxPath -Name "DirectXUserGlobalSettings" -ErrorAction SilentlyContinue
        Write-Host "✓ Removed DirectX GPU preference" -ForegroundColor Green
        
        # Also remove UserGpuPreferences if it exists
        $userGpuPath = "$directxPath\UserGpuPreferences"
        if (Test-Path $userGpuPath) {
            Remove-Item -Path $userGpuPath -Recurse -Force -ErrorAction SilentlyContinue
            Write-Host "✓ Removed UserGpuPreferences" -ForegroundColor Green
        }
    }
} catch {
    Write-Host "⚠ Could not remove DirectX settings: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Reset device in Device Manager
Write-Host "`nResetting V100 device..." -ForegroundColor Yellow
try {
    $v100Device = Get-CimInstance -ClassName Win32_PnPEntity | Where-Object {$_.Name -like "*Tesla V100*"}
    if ($v100Device) {
        Write-Host "Disabling V100..." -ForegroundColor Gray
        $v100Device | Invoke-CimMethod -MethodName "Disable" -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 3
        
        Write-Host "Re-enabling V100..." -ForegroundColor Gray
        $v100Device | Invoke-CimMethod -MethodName "Enable" -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 3
        
        Write-Host "✓ Device reset completed" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Could not reset device automatically" -ForegroundColor Yellow
    Write-Host "Please manually disable/enable V100 in Device Manager" -ForegroundColor White
}

# Show final status
Write-Host "`nChecking final status..." -ForegroundColor Cyan
Start-Sleep -Seconds 5

if ($v100Key) {
    Write-Host "Final V100 Registry Values:" -ForegroundColor White
    try {
        $finalValues = Get-ItemProperty -Path $v100Key
        Write-Host "  FeatureScore: $($finalValues.FeatureScore) (hex: $([Convert]::ToString($finalValues.FeatureScore, 16)))" -ForegroundColor Gray
        Write-Host "  AdapterType: $($finalValues.AdapterType)" -ForegroundColor Gray
        Write-Host "  EnableMsHybrid: $($finalValues.EnableMsHybrid)" -ForegroundColor Gray
        Write-Host "  GridLicensedFeatures: $($finalValues.GridLicensedFeatures)" -ForegroundColor Gray
    } catch {
        Write-Host "  Could not read final values" -ForegroundColor Yellow
    }
}

$finalStatus = Get-CimInstance -ClassName Win32_VideoController | Where-Object {$_.Name -like "*Tesla V100*"}
if ($finalStatus) {
    Write-Host "`nFinal V100 Status: $($finalStatus.Status)" -ForegroundColor $(if ($finalStatus.Status -eq "OK") { "Green" } else { "Red" })
    if ($finalStatus.ConfigManagerErrorCode -ne 0) {
        Write-Host "Error Code: $($finalStatus.ConfigManagerErrorCode)" -ForegroundColor Red
    }
}

Write-Host "`n=== Restoration Summary ===" -ForegroundColor Magenta
Write-Host "✓ V100 restored to original TCC compute mode" -ForegroundColor Green
Write-Host "✓ All hybrid GPU settings removed" -ForegroundColor Green
Write-Host "✓ DirectX preferences cleared" -ForegroundColor Green
Write-Host "✓ Intel settings restored" -ForegroundColor Green

Write-Host "`nRECOMMENDATIONS:" -ForegroundColor Yellow
if ($finalStatus -and $finalStatus.Status -eq "OK") {
    Write-Host "✓ V100 should now work normally for:" -ForegroundColor Green
    Write-Host "  - CUDA compute tasks" -ForegroundColor White
    Write-Host "  - AI/ML workloads" -ForegroundColor White
    Write-Host "  - Scientific computing" -ForegroundColor White
    Write-Host "  - nvidia-smi should work" -ForegroundColor White
} else {
    Write-Host "⚠ If V100 still has issues:" -ForegroundColor Yellow
    Write-Host "  1. Restart computer" -ForegroundColor White
    Write-Host "  2. Reinstall NVIDIA driver" -ForegroundColor White
    Write-Host "  3. Use System Restore if needed" -ForegroundColor White
}

Write-Host "`nFor better SolidWorks performance:" -ForegroundColor Cyan
Write-Host "  - Optimize Intel UHD Graphics 730 settings" -ForegroundColor White
Write-Host "  - Adjust SolidWorks graphics options" -ForegroundColor White
Write-Host "  - Consider adding a professional graphics card" -ForegroundColor White

Write-Host "`nBackup saved to: $backupPath" -ForegroundColor Gray
Read-Host "`nPress Enter to exit"
