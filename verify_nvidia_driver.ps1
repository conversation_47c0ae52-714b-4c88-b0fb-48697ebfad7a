# NVIDIA Driver Verification Script
# Check if Tesla V100 driver is properly installed

Write-Host "=== NVIDIA Tesla V100 Driver Verification ===" -ForegroundColor Green
Write-Host ""

# Check if nvidia-smi is available
try {
    $nvidiaSmiPath = "C:\Program Files\NVIDIA Corporation\NVSMI\nvidia-smi.exe"
    if (Test-Path $nvidiaSmiPath) {
        Write-Host "✓ NVIDIA-SMI found" -ForegroundColor Green
        
        # Run nvidia-smi to get driver info
        Write-Host "Running nvidia-smi..." -ForegroundColor Yellow
        & $nvidiaSmiPath
        Write-Host ""
        
        # Get detailed GPU info
        Write-Host "Getting detailed GPU information..." -ForegroundColor Yellow
        & $nvidiaSmiPath -q -d MEMORY,UTILIZATION,TEMPERATURE,POWER
        
    } else {
        Write-Host "✗ NVIDIA-SMI not found" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Error running nvidia-smi: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Windows Device Manager Info ===" -ForegroundColor Cyan

# Check GPU info from Windows
try {
    $gpuInfo = Get-CimInstance -ClassName Win32_VideoController | Where-Object {$_.Name -like "*NVIDIA*"}
    foreach ($gpu in $gpuInfo) {
        Write-Host "GPU Name: $($gpu.Name)" -ForegroundColor White
        Write-Host "Driver Version: $($gpu.DriverVersion)" -ForegroundColor White
        Write-Host "Driver Date: $($gpu.DriverDate)" -ForegroundColor White
        Write-Host "Status: $($gpu.Status)" -ForegroundColor White
        Write-Host ""
    }
} catch {
    Write-Host "✗ Error getting GPU info: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "=== Recommendations ===" -ForegroundColor Magenta
Write-Host "1. If driver version is 551.78 or newer, the update was successful" -ForegroundColor White
Write-Host "2. For SolidWorks optimization, check NVIDIA Control Panel settings" -ForegroundColor White
Write-Host "3. Consider adding a professional graphics card for better CAD performance" -ForegroundColor White

Read-Host "Press Enter to exit"
